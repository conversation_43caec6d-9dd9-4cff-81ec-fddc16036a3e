

# **From Clinical Theory to Mobile Reality: A Technical Blueprint for an OPEP Monitoring Application**

## **Section 1: Foundational Principles of OPEP Therapy and the Acapella Device**

To develop a robust mobile application for monitoring Oscillatory Positive Expiratory Pressure (OPEP) device usage, a foundational understanding of the device's clinical purpose and mechanical function is paramount. The audio signal that the application will analyze is not a biological artifact but the product of a specific mechanical process driven by a prescribed breathing technique. Comprehending this context is the first step in designing an effective and reliable monitoring algorithm.

### **Mechanism of Action (MoA) of OPEP Devices**

OPEP devices, such as the Smiths Medical Acapella or the Trudell Aerobika, are non-pharmacological tools designed to aid in airway clearance for patients with conditions like Chronic Obstructive Pulmonary Disease (COPD), cystic fibrosis, and bronchiectasis.1 Their therapeutic effect is achieved through a dual mechanism: the application of Positive Expiratory Pressure (PEP) and the introduction of airway oscillations.2  
First, the device provides a slight resistance as the patient exhales. This resistance generates positive pressure within the airways, typically in the therapeutic range of 10 to 20 cm of water (cm H2​O).4 This back pressure effectively "splints" the airways, holding them open for a longer duration and preventing premature collapse, especially in smaller, weaker air passages.2 This stenting action allows air to travel via collateral channels to get behind trapped mucus, helping to dislodge it from the airway walls.6  
Second, and central to the audio-based monitoring approach, these devices incorporate a mechanism to create oscillations. The Acapella device, the subject of the provided research, utilizes a counterweighted lever and magnet system.2 As the user exhales, the airflow passes through a pivoting cone that is intermittently occluded by this rocker mechanism. This action chops the steady stream of exhaled air into a series of rapid pulses, creating high-frequency oscillations.2 These vibrations are transmitted through the air column to the chest wall, acting as a form of "internal physiotherapy" to help shake mucus loose from the airway surfaces, making it easier to mobilize and clear with subsequent coughing.3 The audible "fluttering" sound, which is the target for analysis, is the direct acoustic manifestation of these mechanical oscillations.9  
The sound source is therefore a mechanical process, not a biological one. It is the sound of a small, breath-powered engine operating. This distinction is fundamental. Unlike a human voice, where pitch is controlled by complex neuromuscular actions, the flutter frequency of the device is governed by the physics of the rocker mechanism being driven by the force of the user's exhalation (pressure). This creates a highly predictable and reliable relationship between the driving pressure and the resulting oscillation frequency, making a regression model not only possible but exceptionally robust. The application's purpose is not to analyze a variable biological sound, but to measure the operational frequency of a mechanical instrument.

### **The User Breathing Pattern**

The effectiveness of OPEP therapy is highly dependent on the user's adherence to a specific breathing protocol. This protocol is not a simple exhalation but a structured cycle designed to maximize both the pressure and oscillatory effects of the device.3 The typical procedure is as follows:

1. **Positioning and Relaxation:** The user sits or lies in a comfortable, relaxed position.3  
2. **Inhalation and Breath-Hold:** The user takes a breath that is deeper than normal, but not a full, maximal inhalation. This is followed by a breath-hold of approximately 2-3 seconds.6 This hold allows for more even distribution of air within the lungs, helping air to get behind mucus plugs.  
3. **Mouthpiece Seal:** A tight seal is formed with the lips around the device's mouthpiece to ensure no air escapes.3  
4. **Active Exhalation:** The user exhales actively and steadily through the device, but not with maximum force. The duration of exhalation should be approximately 3-4 times longer than the inhalation phase.7 During this phase, the user should feel the characteristic vibrations in their chest, not their cheeks, which would indicate an improper technique.3  
5. **Repetition and Clearance:** This cycle of breaths is repeated for a set number, typically between 8 and 15 times. Following a set of breaths, the user performs one or two "huff" coughs, which are forceful exhalations with an open glottis, to clear the mobilized sputum from the larger airways.3  
6. **Session Duration:** A complete therapy session consists of repeating these cycles for a duration of 10 to 20 minutes.6

This prescribed breathing technique creates a highly predictable audio signal envelope. A session will consist of distinct periods of the flutter sound, corresponding to the long exhalations, separated by periods of relative silence during inhalation and breath-holds. This on/off pattern is a powerful feature that can be exploited by the mobile application. The problem is not one of continuous audio analysis. Instead, the application can first implement a computationally inexpensive, energy-based "exhalation detector" to identify these active flutter periods. The more complex and CPU-intensive pitch detection algorithm then only needs to be executed during these identified segments. This approach significantly reduces the computational load and conserves battery life, transforming the problem from "analyze all incoming audio" to the much more efficient "find the exhalations, then analyze the pitch within them."

## **Section 2: Analysis of the Pitch-to-Pressure Correlation Study**

The provided research paper presents compelling visual evidence to validate the core hypothesis: that the audible pitch of the Acapella device's flutter can be used to accurately and reliably predict the therapeutic expiratory pressure. An analysis of the key figures reveals the strength of this correlation and, crucially, a finding that dramatically simplifies the logic required for a mobile application.

### **Figure 3: A Single Session Deconstructed**

Image 1 (Figure 3 in the paper) provides a detailed, multi-layered view of the data from a single 15-second session, demonstrating the algorithm's inputs, outputs, and performance.4

* **Top Graph (Pressure vs. Predicted Pressure):** This graph illustrates the ultimate goal and success of the model. The blue line represents the ground truth pressure as measured by a scientific instrument (cm H2​O), while the orange line represents the pressure *predicted* by the algorithm using only the pitch extracted from the audio signal. The green shaded areas highlight the two exhalation periods within the session. The remarkably close tracking of the predicted pressure (orange) to the actual pressure (blue) within these green zones provides a strong visual confirmation that the model is working as intended in a real-time context.  
* **Second Graph (Audio Pitch vs. Pressure Pitch):** This is arguably the most critical validation plot in the entire study. The blue line shows the dominant vibration frequency (pitch) in Hertz (Hz) as detected from the raw *audio* signal captured by an iPhone microphone. The orange line shows the pitch detected by applying the *exact same pitch detection algorithm* to the raw *pressure* signal from the pressure sensor. The fact that these two distinct signals yield a nearly identical pitch profile is definitive proof that the audible flutter is a high-fidelity acoustic representation of the underlying pressure oscillations. It confirms that the microphone is accurately capturing the essential frequency information.  
* **Bottom Two Graphs (Raw Data):** These plots reveal the nature of the raw signals the algorithm must contend with. The third graph shows the raw audio data, a complex waveform with significant noise and fluctuating amplitude. The fourth graph shows the raw pressure data, which consists of a large DC offset (the mean positive pressure) with a high-frequency oscillatory component superimposed on it. These plots visually articulate the engineering challenge: to reliably extract a clean, low-frequency (10-20 Hz) pitch from these complex and seemingly noisy raw inputs. The success shown in the upper graphs is a testament to the effectiveness of the signal processing pipeline.

### **Figure 4: The Linear Model for Pressure Prediction**

Image 3 and Image 4 (Figure 4 in the paper) present the aggregate data from all test subjects and sessions, establishing the mathematical relationship between pitch and pressure.4  
The scatter plot displays 9,993 individual data points, where each point represents a paired observation of the dominant vibration frequency detected from the audio (x-axis) and the simultaneously measured pressure (y-axis). The data points form a tight, linear cluster, indicating a strong, positive correlation between the two variables.  
A linear regression analysis was performed on this data, yielding the following model, which forms the core predictive logic of the application:  
Pressure=−4.659+1.119×Pitch  
The goodness-of-fit for this model is quantified by the coefficient of determination, r2=0.886. An r2 value of this magnitude is exceptionally high, particularly for data involving human subjects. It signifies that 88.6% of the variability in the measured pressure can be statistically explained by the change in the detected audio pitch.4 This strong predictive power validates the use of this simple linear equation as the basis for the application's pressure estimation.

### **Figure 5: The Critical Finding of Generalizability**

Image 2 (Figure 5 in the paper) visualizes the same dataset as Figure 4 but uses color coding to test for confounding variables, leading to the single most important conclusion for application development.4

* **Top Graph (Colored by Resistance Setting):** The data points are colored based on the Acapella device's resistance setting—blue for setting '1' (lowest resistance) and orange for setting '5' (highest resistance). The plot clearly shows that points from both settings are thoroughly intermingled and conform to the same linear trend. There is no evidence of separate clusters or different slopes for the two settings.  
* **Bottom Graph (Colored by Test Person):** Here, the data points are colored according to each of the 19 unique test subjects, who were chosen for their diversity in age, height, weight, and other anatomical factors. As with the resistance settings, the data from all individuals falls along the same, single regression line.

The implication of this generalizability is profound. It demonstrates that a single, universal model is sufficient to predict pressure for all users, regardless of their individual physiology or the specific resistance setting they use on the device.4 This finding vastly simplifies the requirements for the mobile application. There is no need for user-specific calibration procedures, complex user profiles that account for biometrics, or different internal models for the various device settings. The core computational logic of the application, once the pitch has been accurately determined, is reduced to the single line of code derived from the linear model. This de-risks the project significantly, transforming it from a scientific experiment that might work for a subset of users into a robust engineering task with a high probability of success across a broad population.

## **Section 3: A Programmer's Guide to the Autocorrelation-Based Pitch Detection Algorithm**

The success of the pressure prediction model hinges entirely on the ability to accurately and efficiently determine the flutter pitch from a raw audio stream. The research paper details a custom pitch detection algorithm in its appendix, which is specifically tuned for this task. It is not a generic, off-the-shelf pitch detector but a purpose-built, time-domain method optimized for both performance and the unique characteristics of the OPEP signal. A step-by-step breakdown of this algorithm is essential for any developer seeking to replicate the paper's results.4  
The algorithm, implemented as a stateful C++ class named pitchTracker, operates on incremental chunks of audio data. Its core logic can be deconstructed into the following stages:

1. **Configuration:** The algorithm is initialized with a defined target frequency range. For the Acapella device, this is set to \[flower​,fupper​\]=\[10 Hz,40 Hz\]. This constrains the search space to only physiologically relevant flutter frequencies, immediately filtering out irrelevant noise and harmonics outside this band.  
2. **Initial Downsampling:** The raw audio signal, typically sampled at Fs​=44.1 kHz or 48 kHz, is aggressively downsampled by a factor of 45\. This reduces the sampling rate to approximately 1 kHz. This is a critical performance optimization. It drastically reduces the number of data points for all subsequent calculations, making real-time processing on a mobile CPU feasible. Since the maximum target frequency is 40 Hz, a new Nyquist frequency of \~500 Hz is more than sufficient to prevent aliasing of the fundamental flutter frequency.  
3. **High-Pass Filtering (Formant Removal):** A smoothed version of the raw audio is calculated by averaging over a window size corresponding to the smallest expected "formant" frequency (around 250 Hz). This smoothed signal is then subtracted from the original raw signal. From a signal processing perspective, this is a simple but effective high-pass filter. It removes very low-frequency components, such as DC offset from the microphone or the low-frequency "rumble" of breath noise, which are not part of the mechanical flutter signal.  
4. **Signal Squaring and Energy Conversion:** The result of the filtering step is squared. This is a crucial non-linear transformation. It rectifies the signal, making all values positive, and fundamentally changes the signal's representation from amplitude to energy. Squaring the values emphasizes the peaks of the waveform, making the periodic nature of the flutter more pronounced and easier to detect in the subsequent autocorrelation step.  
5. **"Audio Energy" Signal Creation:** The squared signal is downsampled a second time by averaging contiguous, non-overlapping sets of 45 samples. This completes the transformation into what the paper refers to as the "audio energy" signal. This new signal is a smoothed envelope representing the energy fluctuations of the original audio at the target flutter frequencies.  
6. **Gaussian Smoothing:** The audio energy signal is convolved with a Gaussian filter. The width of this filter is carefully chosen to be a fraction of the period of the highest frequency being sought (40 Hz). This step acts as a low-pass filter on the *energy envelope itself*, further smoothing out spurious, sharp peaks and making the true periodic peaks related to the device's flutter even more distinct and easier to track.  
7. **"Economical" Autocorrelation:** This is the core pitch-finding step. Autocorrelation is a mathematical tool that measures the similarity of a signal with a delayed copy of itself as a function of the delay. The delay at which the similarity is highest corresponds to the signal's fundamental period. The algorithm employs several key optimizations to make this process "economical":  
   * **Stateful Search:** Rather than computing the autocorrelation across all possible lags for every new chunk of audio, the algorithm is stateful. Once a pitch has been successfully detected, the search for the next autocorrelation peak is focused in a narrow window *around the previously detected period*. This dramatically reduces the number of calculations required for each frame.  
   * **Confidence Threshold:** A candidate pitch is only accepted if the peak of the autocorrelation function has a value greater than 0.6. This acts as a confidence score, ensuring that the algorithm only reports pitches that correspond to a strongly periodic signal, effectively rejecting noise or non-flutter sounds.  
8. **Stateful Smoothing for Output Stability:** The raw output of the autocorrelation can still be jittery. To provide a smooth, stable value for the user interface, the algorithm implements a sophisticated smoothing system based on a moving average.  
   * **Run Length:** A runLength counter tracks the number of consecutive successful pitch detections. It is incremented when a valid pitch is found and is reset or decremented when a detection is missed or is inconsistent with the running average. This acts as a debouncing and stability-checking mechanism.  
   * **Adaptive Mixing:** A mix value is calculated based on the current runLength and a configurable decayRate (set to 0.8 in the paper). This mix value determines how much influence a new pitch reading has on the running average.  
   * **Moving Average Update:** The moving average of the signal's *period* is updated using the formula: MovingAve=movingAve×mix+newvalue×(1−mix). When the runLength is low (the signal is unstable), the mix value is high, meaning the old average is trusted more. When the runLength is high (the signal is stable), the mix value is lower, allowing the average to track new values more quickly.  
   * **Final Pitch Calculation:** The final, reported pitch is calculated as the reciprocal of this smoothed moving average period: Pitch=1/movingAvePeriod.

This entire stateful system is designed not just to find a single pitch value in an isolated buffer, but to track that pitch smoothly and reliably over time. A developer must implement this as a stateful class, where the internal state (moving averages, run length) is preserved between calls that process new audio chunks. A stateless implementation would produce a noisy, flickering output unsuitable for user feedback.

## **Section 4: A Practical Roadmap for iOS Implementation**

Translating the validated research into a functional iOS application requires a clear technical strategy. This involves establishing a real-time audio pipeline, implementing the core pitch detection algorithm, and critically, developing a robust strategy for handling noise and ensuring signal integrity in uncontrolled real-world environments.

### **A. The Core Audio Pipeline with AVAudioEngine**

Apple's AVFoundation framework, and specifically the AVAudioEngine class, provides the ideal toolset for constructing the necessary real-time audio processing pipeline.11

1. **Audio Session Configuration:** The first step in the application's lifecycle is to configure and activate the shared AVAudioSession. It should be configured with the .record or .playAndRecord category to enable microphone input. The application must also request microphone permission from the user, providing a clear explanation for its necessity in the Info.plist file.12  
2. **Engine and Node Setup:** An instance of AVAudioEngine will manage the graph of audio nodes. For this application, a simple graph is sufficient: the engine's inputNode (representing the microphone) is connected to the mainMixerNode, which is then connected to the outputNode. While the output is not strictly needed for analysis, connecting the graph to an output is often required for the engine to process audio.14 The output volume can be set to zero.  
3. **Capturing Audio Buffers:** The key to real-time analysis is the installTap(onBus:bufferSize:format:block:) method, called on the inputNode.15 This installs a "tap" that provides a callback block, which is executed repeatedly with new audio data. This block receives two crucial parameters: an  
   AVAudioPCMBuffer and an AVAudioTime.  
   * The AVAudioPCMBuffer contains the raw audio samples for a small chunk of time. The bufferSize can be configured to balance latency and processing overhead; the paper suggests a size corresponding to 0.1 seconds of audio, which at a 44.1 kHz sample rate would be 4410 frames.4  
   * The raw sample data is accessible via the buffer's floatChannelData property, which provides a pointer to an array of Float values. This array is the direct input for the pitch detection algorithm.17

### **B. Implementing the Pitch Detection Algorithm**

There are two primary paths for implementing the custom pitch detection algorithm on iOS. The choice between them represents a classic trade-off between development speed and code-base purity.

* **Path 1: C++ Wrapper (Recommended for Fidelity and Speed-to-Market):** The research paper explicitly states that a C++ implementation of the pitchTracker class was created and validated.4 The most direct and lowest-risk path to achieving the same performance and accuracy is to incorporate this C++ code directly. This is accomplished by creating an Objective-C++ wrapper class (a file with a  
  .mm extension). This wrapper would instantiate and hold the C++ pitchTracker object. It would then expose a clean, Swift-friendly interface, such as a single method func process(buffer: AVAudioPCMBuffer) \-\> Float, which handles the necessary data marshalling between the Swift AVAudioPCMBuffer and the C-style array pointer expected by the C++ code. This approach guarantees perfect fidelity with the validated algorithm. While a comprehensive framework like JUCE could also be used to manage C++ audio code, it is likely overkill for this specific, self-contained task.18  
* **Path 2: Native Swift Rewrite with the Accelerate Framework:** For a pure-Swift solution, the algorithm can be re-implemented using Apple's Accelerate framework, which provides highly optimized functions for digital signal processing (DSP).20  
  * The most computationally intensive step, the autocorrelation, can be implemented with high performance using the vDSP.correlate function.21 This function is vectorized and heavily optimized for Apple's ARM-based CPUs.  
  * Other operations required by the algorithm, such as downsampling (through decimation), filtering, and vector arithmetic (squaring, averaging), can also be implemented efficiently using other functions within the vDSP library.  
  * This path requires more development effort and a deeper understanding of DSP to ensure the implementation is a faithful and correct translation of the original. However, it results in a more modern, single-language codebase.

### **C. The Critical Challenge of Noise and Signal Isolation**

The study protocol noted that the microphone was placed "with no particular care," mimicking real-world use.4 Therefore, the application must be robust against various types of noise, including ambient background sounds (e.g., television, conversations) and user-generated noise (e.g., breath sounds, coughs, device handling). A production-quality application will require a multi-layered strategy for noise mitigation.

1. **Layer 1: Energy-Based Gating:** As established previously, the first and most effective line of defense is to only activate the pitch detection logic during periods of high-energy exhalation. A simple power or Root Mean Square (RMS) threshold can be calculated from each incoming audio buffer. When the energy surpasses this threshold, the "exhalation" state begins; when it drops below the threshold for a sustained period, the state ends. This macro-level gating will inherently ignore most low-level background noise and prevent the algorithm from attempting to analyze silence or quiet breathing.  
2. **Layer 2: Signal Filtering:** The paper's algorithm already incorporates a high-pass filter to remove low-frequency rumble.4 This is the second layer of defense. Developers should be cautious about using Apple's built-in voice processing. The  
   AVAudioEngine input node has a setVoiceProcessingEnabled(true) option, which enables echo cancellation and noise suppression.23 However, this system is tuned specifically for the characteristics of  
   *human speech*. It is highly likely that it would misinterpret the 10-40 Hz mechanical flutter of the OPEP device as undesirable low-frequency noise and attempt to suppress it, thereby corrupting the very signal the app needs to measure. This feature should be tested with extreme care and is likely best left disabled.  
3. **Layer 3: Algorithmic Confidence:** The algorithm itself provides a third layer of defense with its confidence check. By only accepting autocorrelation peaks with a correlation value greater than 0.6, it intrinsically rejects frames where a strong, clear periodic signal is not present.4  
4. **Layer 4 (Advanced): Active Noise Cancellation:** If testing in real-world environments reveals that the first three layers are insufficient, a more advanced active noise cancellation technique could be implemented as a pre-processing step.  
   * **Spectral Subtraction:** This classic DSP technique involves estimating the frequency spectrum of the background noise (e.g., from a period of silence before the user begins) and subtracting this "noise profile" from the spectrum of the active signal.24 This can be effective but is complex to implement robustly in real-time, especially with non-stationary noise.  
   * **AI-Based Noise Reduction:** Modern AI-powered libraries, such as Krisp.ai (which offers an SDK), excel at separating a target sound from background noise.26 While typically trained on human voice, these models may be effective enough to clean the OPEP audio signal before it is passed to the pitch detection algorithm. This approach introduces an external dependency and potential licensing costs but could offer the most powerful solution for noise cancellation.

For an initial product, a combination of the first three layers—energy gating, the paper's built-in filtering, and the algorithm's confidence threshold—should provide a robust foundation.

## **Section 5: Survey of Alternative Frameworks and Libraries**

While the paper's custom algorithm presents a strong case for a bespoke implementation, a thorough analysis requires surveying the landscape of available tools, both native to iOS and from third parties. This allows for an informed decision on the optimal technology stack.

### **Native iOS Frameworks**

Apple provides a powerful suite of frameworks for audio and computation. Understanding their specific roles is key to using them correctly.

* **AVFoundation & AVAudioEngine:** As detailed in the previous section, this is the primary framework for managing the real-time audio pipeline. It is the correct and necessary tool for audio input/output, node graph management, and capturing raw audio buffers for analysis.12  
* **Accelerate (specifically vDSP):** This is the framework for high-performance, on-CPU numerical computation. It is not an audio framework itself, but it provides the building blocks (e.g., FFTs, filters, vector arithmetic, correlation functions) to implement custom DSP algorithms, like the one in the paper, with maximum efficiency in Swift.20 It is the ideal tool for a native rewrite of the pitch detection logic.  
* **SoundAnalysis:** This framework is often mistaken as a tool for pitch detection, but its purpose is fundamentally different. SoundAnalysis is designed for *sound classification*. It uses machine learning models—either a comprehensive built-in model that recognizes over 300 sound types or a custom model trained with Create ML—to analyze an audio stream and assign a categorical label to the sounds it identifies (e.g., "speech," "dog bark," "music," "typing").29 It does not provide a continuous, quantitative output like frequency in Hertz. Therefore,  
  **SoundAnalysis is the wrong tool for this specific task**, which requires a regression output (pressure), not a classification label.

### **Third-Party Pitch Detection Libraries**

Several open-source libraries exist for pitch detection. However, their suitability for this project varies based on their underlying algorithm, performance profile, and, critically, their software license.

| Library/Algorithm | Implementation | Underlying Method | Suitability for OPEP Flutter | Performance Profile | License | Key Snippets |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| **Paper's Algorithm** | C++ | Custom Time-Domain Autocorrelation | **Very High** (custom-designed) | **Very Lightweight** (20s audio in 50ms on laptop) | Proprietary (Smiths Medical) | 4 |
| **aubio** | C (Python bindings) | Multiple (YIN, spectral comb, etc.) | **Moderate** (general purpose) | Lightweight, real-time capable | **GPL** (viral, requires open-sourcing app) | 31 |
| **Beethoven** | Swift | Wraps multiple algorithms (YIN, HPS, etc.) | **Moderate** (general purpose) | Good for prototyping, depends on chosen algo | MIT (permissive) | 32 |
| **TensorFlow Lite (SPICE)** | ML Model | ML (Self-Supervised Pitch Estimation) | **Low-to-Moderate** (trained on voice/music) | Can be heavyweight, requires TFLite runtime | Apache 2.0 (permissive) | 33 |
| **basic-pitch (Spotify)** | ML Model | ML (Polyphonic Note Transcription) | **Low** (designed for complex music, overkill) | Likely heavyweight, optimized for polyphony | Apache 2.0 (permissive) | 35 |

This comparative analysis leads to a clear conclusion. The paper's custom algorithm is, by design, the most suitable for the task. Among the open-source alternatives, libraries like aubio are technically capable but are encumbered by a GPL license, making them unsuitable for a closed-source commercial application. The Beethoven library is a viable option for rapid prototyping due to its permissive MIT license and easy Swift integration, but its performance and accuracy with the specific OPEP signal would need to be validated. The machine learning models (SPICE, basic-pitch) are trained on different problem domains (voice and music) and are likely to be both less accurate for this mechanical sound and more computationally expensive than the specialized time-domain algorithm.

## **Section 6: Strategic Recommendations and Development Blueprint**

Synthesizing the analysis of the OPEP device mechanics, the clinical study, the custom algorithm, and the iOS technology landscape, a clear strategic blueprint for development emerges. This blueprint outlines the recommended technology stack, a phased development plan, and a proactive assessment of potential risks.

### **A. Proposed Technology Stack**

The optimal technology stack should prioritize fidelity to the validated research, performance on mobile hardware, and a clean development path.

* **Core Logic:** The primary recommendation is to **faithfully replicate the paper's custom autocorrelation-based algorithm**. The evidence of its high performance and specific tuning for the OPEP signal makes it the superior choice over any general-purpose library.4 Its proven accuracy and efficiency are the foundation of the project's viability.  
* **Implementation Strategy:**  
  * **Option A (High Fidelity, Lower Risk):** Create an **Objective-C++ wrapper** around the existing, validated C++ pitchTracker code from the study. This is the most pragmatic and lowest-risk path to achieving the documented performance and accuracy. It leverages the existing intellectual property directly and minimizes the chance of implementation errors.  
  * **Option B (Native Performance, Higher Effort):** For a pure-Swift codebase, conduct a native rewrite of the algorithm using Apple's **Accelerate/vDSP framework**. This is the most "Apple-native" approach and will yield excellent on-device performance but requires meticulous implementation and rigorous validation against the original algorithm's output to ensure correctness.  
* **Audio I/O:** The real-time audio pipeline should be built using **AVAudioEngine**. Its robustness, low-latency capabilities, and direct integration with the iOS ecosystem make it the standard and correct choice for this task.

### **B. Phased Development Plan**

A structured, phased approach will ensure logical progression and allow for testing and validation at each critical stage.

1. **Phase 1: Audio Pipeline & Data Capture:** The initial focus should be on the foundational audio infrastructure. Build the AVAudioEngine pipeline, configure the AVAudioSession, and implement the input node tap. The goal of this phase is to confirm that the application can successfully receive AVAudioPCMBuffers in real-time and log their raw data to the console.  
2. **Phase 2: Exhalation Detection:** Before tackling pitch, implement the simpler energy-based exhalation detector. This involves creating a state machine that calculates the RMS or average power of each buffer and uses a threshold to toggle between "listening" and "idle" states, mimicking the green zones in the paper's Figure 3\.  
3. **Phase 3: Core Algorithm Implementation:** Implement the chosen strategy from the technology stack (C++ wrapper or native Swift rewrite). This phase should be conducted in isolation from the real-time pipeline. The algorithm should be tested against pre-recorded audio files or synthetically generated signals with known frequencies to validate that its pitch output is accurate.  
4. **Phase 4: Integration and Pressure Conversion:** Integrate the validated pitch detector into the audio pipeline. The detector should only be fed audio buffers during the "listening" state identified in Phase 2\. Implement the simple linear model (Pressure=−4.659+1.119×Pitch) to convert the algorithm's final pitch output into a pressure value in cm H2​O.  
5. **Phase 5: UI & Real-Time Feedback:** Develop the user interface. This should include a real-time display (e.g., a gauge or bar) that shows the estimated pressure and clearly indicates whether the user is within, above, or below the target therapeutic range of 10-20 cm H2​O.  
6. **Phase 6: Real-World Testing & Noise Hardening:** Conduct extensive testing of the complete application in a variety of real-world, noisy environments. If the layered noise mitigation strategy (gating, filtering, confidence threshold) proves insufficient, this phase would involve implementing and testing an advanced noise reduction layer, such as an AI-based SDK or a custom spectral subtraction module.

### **C. Risk Assessment and Mitigation**

Several potential risks should be anticipated and addressed proactively.

* **Risk 1: Microphone Variability:** Different iPhone models and generations possess microphones with varying frequency responses, sensitivities, and noise floors. An older device might struggle to capture the low-frequency (10-40 Hz) flutter as clearly as a newer one, potentially affecting the algorithm's accuracy.36  
  * **Mitigation:** The development and QA process must include testing on a wide range of target devices, from the oldest supported model to the newest. If variability proves to be a significant issue, consider implementing an optional, one-time "noise floor calibration" step in the app's settings. This would involve having the user remain silent for a few seconds, allowing the app to measure the ambient noise profile of their specific environment and device, and adjust its internal energy thresholds accordingly.  
* **Risk 2: Computational Performance on Older Devices:** While the algorithm is designed to be efficient, real-time audio processing can still be demanding on the CPU of older, less powerful devices.  
  * **Mitigation:** Continuously profile the application's CPU usage on the lowest-spec target device throughout development. If performance becomes a bottleneck, a simple mitigation is to increase the audio buffer size for the installTap method (e.g., from 0.1s to 0.2s). This would reduce the frequency of callbacks and give the CPU more time to process each chunk, at the cost of slightly increased latency in the UI feedback.  
* **Risk 3: Intellectual Property (IP):** The custom C++ algorithm and the specific coefficients of the linear model are the direct result of research conducted and funded by Smiths Medical.4 They constitute valuable intellectual property.  
  * **Mitigation:** This is a critical business and legal risk, not a technical one. Before any commercial development begins, legal counsel must be consulted to determine the IP status of the algorithm and model. It may be necessary to license the technology from Smiths Medical. Attempting to commercialize a product based directly on this proprietary research without clearance could lead to significant legal challenges. This risk must be resolved before committing substantial development resources.

#### **Works cited**

1. Guide: Explore the Best OPEP Device for Your Respiratory Needs \- Aussie Pharma Direct, accessed August 13, 2025, [https://www.aussiepharmadirect.com.au/blogs/news/your-guide-to-opep-devices-explore-the-best-opep-device-for-your-respiratory-needs](https://www.aussiepharmadirect.com.au/blogs/news/your-guide-to-opep-devices-explore-the-best-opep-device-for-your-respiratory-needs)  
2. Acapella \- Physiopedia, accessed August 13, 2025, [https://www.physio-pedia.com/Acapella](https://www.physio-pedia.com/Acapella)  
3. Aerobika OPEP Device | Royal Free London \- Royal Free Hospital, accessed August 13, 2025, [https://www.royalfree.nhs.uk/patients-and-visitors/patient-information-leaflets/aerobika-opep-device](https://www.royalfree.nhs.uk/patients-and-visitors/patient-information-leaflets/aerobika-opep-device)  
4. AcapellaPitchDetectionV3.pdf  
5. Positive Expiratory Pressure Therapy \- Bronchiectasis Toolbox, accessed August 13, 2025, [https://bronchiectasis.com.au/physiotherapy/techniques/positive-expiratory-pressure-therapy](https://bronchiectasis.com.au/physiotherapy/techniques/positive-expiratory-pressure-therapy)  
6. How to use the Acapella® Choice \- Medway Community Healthcare, accessed August 13, 2025, [https://www.medwaycommunityhealthcare.nhs.uk/devuret/2025/03/Acapella\_leaflet.pdf](https://www.medwaycommunityhealthcare.nhs.uk/devuret/2025/03/Acapella_leaflet.pdf)  
7. Oscillating PEP | Home Care & Instructions \- Cincinnati Children's Hospital, accessed August 13, 2025, [https://www.cincinnatichildrens.org/health/o/oscillating-pep](https://www.cincinnatichildrens.org/health/o/oscillating-pep)  
8. Acapella® Choice | Great Ormond Street Hospital, accessed August 13, 2025, [https://www.gosh.nhs.uk/conditions-and-treatments/procedures-and-treatments/acapella-choice/](https://www.gosh.nhs.uk/conditions-and-treatments/procedures-and-treatments/acapella-choice/)  
9. AEROBIKA\* OPEP Device | trudellmed.com, accessed August 13, 2025, [https://www.trudellmed.com/global/en/products/aerobika-opep-device](https://www.trudellmed.com/global/en/products/aerobika-opep-device)  
10. How to clear phlegm from your chest | Positive Expiratory Pressure (PEP) mask \- YouTube, accessed August 13, 2025, [https://www.youtube.com/watch?v=8d\_t3Sub0IY](https://www.youtube.com/watch?v=8d_t3Sub0IY)  
11. Using AVAudioEngine to Record, Compress and Stream Audio on iOS \- Arvindh Sukumar, accessed August 13, 2025, [https://arvindhsukumar.medium.com/using-avaudioengine-to-record-compress-and-stream-audio-on-ios-48dfee09fde4](https://arvindhsukumar.medium.com/using-avaudioengine-to-record-compress-and-stream-audio-on-ios-48dfee09fde4)  
12. Audio playback, recording, and processing | Apple Developer Documentation, accessed August 13, 2025, [https://developer.apple.com/documentation/avfoundation/audio-playback-recording-and-processing](https://developer.apple.com/documentation/avfoundation/audio-playback-recording-and-processing)  
13. Sound Analysis on Apple Platforms \- iteo, accessed August 13, 2025, [https://iteo.com/blog/post/sound-analysis-on-apple-platforms/](https://iteo.com/blog/post/sound-analysis-on-apple-platforms/)  
14. Splitting Signals in Music Apps with AVAudioEngine | by Greg Cerveny \- Medium, accessed August 13, 2025, [https://gmcerveny.medium.com/splitting-signals-in-music-apps-with-avaudioengine-53e144691167](https://gmcerveny.medium.com/splitting-signals-in-music-apps-with-avaudioengine-53e144691167)  
15. ios \- Is it feasable to use AVAudioEngine to detect pitch in real time ..., accessed August 13, 2025, [https://stackoverflow.com/questions/35694236/is-it-feasable-to-use-avaudioengine-to-detect-pitch-in-real-time](https://stackoverflow.com/questions/35694236/is-it-feasable-to-use-avaudioengine-to-detect-pitch-in-real-time)  
16. Digital Signal Processing in Swift | by Ashish Patil \- Medium, accessed August 13, 2025, [https://medium.com/@sheeshnator4000/digital-signal-processing-in-swift-2d91270ef17a](https://medium.com/@sheeshnator4000/digital-signal-processing-in-swift-2d91270ef17a)  
17. Audio Visualization in Swift Using Metal and Accelerate (Part 1\) \- Better Programming, accessed August 13, 2025, [https://betterprogramming.pub/audio-visualization-in-swift-using-metal-accelerate-part-1-390965c095d7](https://betterprogramming.pub/audio-visualization-in-swift-using-metal-accelerate-part-1-390965c095d7)  
18. JUCE: Home, accessed August 13, 2025, [https://juce.com/](https://juce.com/)  
19. juce-framework/JUCE: JUCE is an open-source cross-platform C++ application framework for desktop and mobile applications, including VST, VST3, AU, AUv3, LV2 and AAX audio plug-ins. \- GitHub, accessed August 13, 2025, [https://github.com/juce-framework/JUCE](https://github.com/juce-framework/JUCE)  
20. Accelerate Overview \- Apple Developer, accessed August 13, 2025, [https://developer.apple.com/accelerate/](https://developer.apple.com/accelerate/)  
21. 1D correlation and convolution | Apple Developer Documentation, accessed August 13, 2025, [https://developer.apple.com/documentation/accelerate/1d-correlation-and-convolution](https://developer.apple.com/documentation/accelerate/1d-correlation-and-convolution)  
22. Swift \- read two audio files and calculate their cross-correlation \- Stack Overflow, accessed August 13, 2025, [https://stackoverflow.com/questions/65571299/swift-read-two-audio-files-and-calculate-their-cross-correlation](https://stackoverflow.com/questions/65571299/swift-read-two-audio-files-and-calculate-their-cross-correlation)  
23. avoid background noise and audio echo in AvAudioEngine Swift \- Stack Overflow, accessed August 13, 2025, [https://stackoverflow.com/questions/77994207/avoid-background-noise-and-audio-echo-in-avaudioengine-swift](https://stackoverflow.com/questions/77994207/avoid-background-noise-and-audio-echo-in-avaudioengine-swift)  
24. Spectral Subtraction Mastery \- Number Analytics, accessed August 13, 2025, [https://www.numberanalytics.com/blog/spectral-subtraction-ultimate-guide](https://www.numberanalytics.com/blog/spectral-subtraction-ultimate-guide)  
25. A geometric approach to spectral subtraction \- PMC, accessed August 13, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC2516309/](https://pmc.ncbi.nlm.nih.gov/articles/PMC2516309/)  
26. Krisp \- AI Meeting Assistant with Built-In Noise Cancellation, accessed August 13, 2025, [https://krisp.ai/](https://krisp.ai/)  
27. Noise Cancellation \- iOS Video and Audio Docs \- Stream, accessed August 13, 2025, [https://getstream.io/video/docs/ios/guides/noise-cancellation/](https://getstream.io/video/docs/ios/guides/noise-cancellation/)  
28. Audio \- Apple Developer, accessed August 13, 2025, [https://developer.apple.com/audio/](https://developer.apple.com/audio/)  
29. Sound Analysis | Apple Developer Documentation, accessed August 13, 2025, [https://developer.apple.com/documentation/soundanalysis](https://developer.apple.com/documentation/soundanalysis)  
30. Discover built-in sound classification in SoundAnalysis \- WWDC21 \- Apple Developer, accessed August 13, 2025, [https://developer.apple.com/videos/play/wwdc2021/10036/](https://developer.apple.com/videos/play/wwdc2021/10036/)  
31. aubio, a library for audio labelling, accessed August 13, 2025, [https://aubio.org/](https://aubio.org/)  
32. vadymmarkov/Beethoven: :guitar: A maestro of pitch detection. \- GitHub, accessed August 13, 2025, [https://github.com/vadymmarkov/Beethoven](https://github.com/vadymmarkov/Beethoven)  
33. Pitch Detection with SPICE | TensorFlow Hub, accessed August 13, 2025, [https://www.tensorflow.org/hub/tutorials/spice](https://www.tensorflow.org/hub/tutorials/spice)  
34. Estimating musical scores (pitch) in android with TensorFlow's SPICE model, accessed August 13, 2025, [https://farmaker47.medium.com/estimating-musical-scores-pitch-in-android-with-tensorflows-spice-model-4d712ded96f8](https://farmaker47.medium.com/estimating-musical-scores-pitch-in-android-with-tensorflows-spice-model-4d712ded96f8)  
35. spotify/basic-pitch: A lightweight yet powerful audio-to-MIDI converter with pitch bend detection \- GitHub, accessed August 13, 2025, [https://github.com/spotify/basic-pitch](https://github.com/spotify/basic-pitch)  
36. Improving Pitch Detection, accessed August 13, 2025, [https://groups.google.com/g/audiokit/c/JBnfOuh07DA](https://groups.google.com/g/audiokit/c/JBnfOuh07DA)