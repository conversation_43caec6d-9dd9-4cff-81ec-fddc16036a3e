// PitchTrackerTests.swift
// Comprehensive test suite for the pitch tracker

import XCTest
import Accelerate
@testable import sada

final class ComprehensivePitchTrackerTests: XCTestCase {
    var tracker: PitchTracker!
    var config: PitchConfig!

    override func setUp() {
        super.setUp()
        config = PitchConfig()
        tracker = PitchTracker(config: config)
    }

    // MARK: - Synthetic Signal Tests

    func testKnownFrequencyDetection() {
        let testFrequencies: [Float] = [12, 15, 20, 25, 30, 35, 38]

        for targetFreq in testFrequencies {
            let signal = generateAMSignal(
                carrierFreq: 300,
                modulationFreq: targetFreq,
                duration: 2.0,
                sampleRate: config.sampleRate
            )

            var detectedPitches: [Float] = []
            let chunkSize = config.chunkFrames

            for i in stride(from: 0, to: signal.count - chunkSize, by: chunkSize) {
                signal.withUnsafeBufferPointer { buffer in
                    if let result = tracker.process(
                        buffer: buffer.baseAddress! + i,
                        count: chunkSize,
                        startIndex: i,
                        timestamp: Double(i) / config.sampleRate
                    ) {
                        detectedPitches.append(result.pitchHz)
                    }
                }
            }

            guard !detectedPitches.isEmpty else {
                XCTFail("No pitch detected for frequency \(targetFreq) Hz")
                continue
            }

            let avgPitch = detectedPitches.reduce(0, +) / Float(detectedPitches.count)
            let accuracy = abs(avgPitch - targetFreq)

            XCTAssertLessThan(accuracy, 0.5,
                "Pitch detection accuracy for \(targetFreq) Hz: detected \(avgPitch) Hz")
        }
    }

    func testNoiseRobustness() {
        let cleanSignal = generateAMSignal(carrierFreq: 300, modulationFreq: 20, duration: 3.0)

        // Test various SNR levels
        let snrLevels: [Float] = [20, 15, 10, 5] // dB

        for snr in snrLevels {
            let noisySignal = addGaussianNoise(cleanSignal, snrDB: snr)

            let cleanPitch = detectAveragePitch(cleanSignal)
            let noisyPitch = detectAveragePitch(noisySignal)

            let difference = abs(cleanPitch - noisyPitch)
            let tolerance: Float = snr > 10 ? 1.0 : 2.0 // More tolerance for high noise

            XCTAssertLessThan(difference, tolerance,
                "Noise robustness failed at \(snr) dB SNR: clean=\(cleanPitch), noisy=\(noisyPitch)")
        }
    }

    func testPerformanceBenchmark() {
        let testDuration: Double = 20.0 // 20 seconds of audio
        let signal = generateAMSignal(
            carrierFreq: 300,
            modulationFreq: 20,
            duration: testDuration,
            sampleRate: config.sampleRate
        )

        let startTime = CFAbsoluteTimeGetCurrent()

        let chunkSize = config.chunkFrames
        var processedChunks = 0

        for i in stride(from: 0, to: signal.count - chunkSize, by: chunkSize) {
            signal.withUnsafeBufferPointer { buffer in
                _ = tracker.process(
                    buffer: buffer.baseAddress! + i,
                    count: chunkSize,
                    startIndex: i,
                    timestamp: Double(i) / config.sampleRate
                )
            }
            processedChunks += 1
        }

        let processingTime = CFAbsoluteTimeGetCurrent() - startTime
        let realTimeRatio = testDuration / processingTime

        print("Performance: \(realTimeRatio)x real-time (\(processingTime)s to process \(testDuration)s)")

        // Should achieve at least 100x real-time (research claims 400x)
        XCTAssertGreaterThan(realTimeRatio, 100,
            "Performance target not met: \(realTimeRatio)x real-time")
    }

    // MARK: - Algorithm Component Tests

    func testPressureModelAccuracy() {
        let pressureModel = PressureModel()

        // Test known pitch-pressure pairs from research
        let testCases: [(pitch: Float, expectedPressure: Float)] = [
            (15.0, 12.2),  // Approximate from research Figure 4
            (20.0, 17.7),
            (25.0, 23.2),
            (30.0, 28.7)
        ]

        for testCase in testCases {
            let pitchResult = PitchResult(
                pitchHz: testCase.pitch,
                confidence: 0.8,
                amplitude: 0.5,
                timestamp: 0
            )

            let pressureReading = pressureModel.estimatePressure(from: pitchResult)
            let error = abs(pressureReading.pressure - testCase.expectedPressure)

            XCTAssertLessThan(error, 2.0,
                "Pressure model error for \(testCase.pitch) Hz: expected \(testCase.expectedPressure), got \(pressureReading.pressure)")
        }
    }

    // MARK: - Helper Methods

    private func generateAMSignal(carrierFreq: Float, modulationFreq: Float, duration: Double, sampleRate: Double = 44100) -> [Float] {
        let sampleCount = Int(duration * sampleRate)
        var signal = [Float](repeating: 0, count: sampleCount)

        for i in 0..<sampleCount {
            let t = Float(i) / Float(sampleRate)
            let modulation = 0.5 * (1.0 + sin(2 * .pi * modulationFreq * t))
            let carrier = sin(2 * .pi * carrierFreq * t)
            signal[i] = modulation * carrier
        }

        return signal
    }

    private func addGaussianNoise(_ signal: [Float], snrDB: Float) -> [Float] {
        let signalPower = signal.map { $0 * $0 }.reduce(0, +) / Float(signal.count)
        let noisePower = signalPower / pow(10, snrDB / 10)
        let noiseStd = sqrt(noisePower)

        return signal.map { sample in
            let noise = Float.random(in: -1...1) * noiseStd
            return sample + noise
        }
    }

    private func detectAveragePitch(_ signal: [Float]) -> Float {
        var pitches: [Float] = []
        let chunkSize = config.chunkFrames

        for i in stride(from: 0, to: signal.count - chunkSize, by: chunkSize) {
            signal.withUnsafeBufferPointer { buffer in
                if let result = tracker.process(
                    buffer: buffer.baseAddress! + i,
                    count: chunkSize,
                    startIndex: i,
                    timestamp: Double(i) / config.sampleRate
                ) {
                    pitches.append(result.pitchHz)
                }
            }
        }

        return pitches.isEmpty ? 0 : pitches.reduce(0, +) / Float(pitches.count)
    }
}

