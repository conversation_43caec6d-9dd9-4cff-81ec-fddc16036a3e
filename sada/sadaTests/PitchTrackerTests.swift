// PitchTrackerTests.swift
// Basic synthetic tests for the pitch tracker

import XCTest
@testable import sada

final class PitchTrackerTests: XCTestCase {
    func testDetectsKnownAM() throws {
        var cfg = PitchConfig()
        cfg.sampleRate = 44_100
        cfg.chunkDuration = 0.2 // longer chunk for synthetic
        let tracker = PitchTracker(config: cfg)

        // Generate AM signal: carrier 300 Hz amplitude-modulated at 20 Hz
        let sr = cfg.sampleRate
        let dur: Double = 2.0
        let n = Int(sr * dur)
        var signal = [Float](repeating: 0, count: n)
        for i in 0..<n {
            let t = Double(i) / sr
            let mod = 0.5 * (1.0 + sin(2 * .pi * 20.0 * t))
            let car = sin(2 * .pi * 300.0 * t)
            signal[i] = Float(mod * car)
        }
        var detected: [Float] = []
        let chunk = Int(cfg.chunkDuration * sr)
        var idx = 0
        while idx + chunk <= n {
            signal.withUnsafeBufferPointer { buf in
                if let r = tracker.process(buffer: buf.baseAddress! + idx, count: chunk, startIndex: idx, timestamp: 0) {
                    detected.append(r.pitchHz)
                }
            }
            idx += chunk
        }
        let avg = detected.isEmpty ? 0 : detected.reduce(0, +) / Float(detected.count)
        XCTAssertGreaterThan(avg, 18)
        XCTAssertLessThan(avg, 22)
    }
}

