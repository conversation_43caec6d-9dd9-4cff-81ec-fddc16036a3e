// BufferPool.swift
// Memory pool management for efficient buffer allocation and reuse

import Foundation

class BufferPool {
    private var availableBuffers: [UnsafeMutablePointer<Float>] = []
    private let bufferSize: Int
    private let maxPoolSize: Int
    private let lock = NSLock()

    init(bufferSize: Int, maxPoolSize: Int = 10) {
        self.bufferSize = bufferSize
        self.maxPoolSize = maxPoolSize
    }

    func getBuffer() -> UnsafeMutablePointer<Float> {
        lock.lock()
        defer { lock.unlock() }
        
        if let buffer = availableBuffers.popLast() {
            // Zero out the buffer for clean reuse
            buffer.initialize(repeating: 0.0, count: bufferSize)
            return buffer
        }
        
        // Allocate new buffer if pool is empty
        let newBuffer = UnsafeMutablePointer<Float>.allocate(capacity: bufferSize)
        newBuffer.initialize(repeating: 0.0, count: bufferSize)
        return newBuffer
    }

    func returnBuffer(_ buffer: UnsafeMutablePointer<Float>) {
        lock.lock()
        defer { lock.unlock() }
        
        // Only keep buffers if we haven't exceeded max pool size
        if availableBuffers.count < maxPoolSize {
            availableBuffers.append(buffer)
        } else {
            // Deallocate excess buffers
            buffer.deinitialize(count: bufferSize)
            buffer.deallocate()
        }
    }
    
    func clear() {
        lock.lock()
        defer { lock.unlock() }
        
        // Deallocate all pooled buffers
        for buffer in availableBuffers {
            buffer.deinitialize(count: bufferSize)
            buffer.deallocate()
        }
        availableBuffers.removeAll()
    }

    deinit {
        clear()
    }
}

// MARK: - Array Buffer Pool for Swift Arrays
class ArrayBufferPool<T> {
    private var availableBuffers: [[T]] = []
    private let bufferSize: Int
    private let maxPoolSize: Int
    private let lock = NSLock()
    private let defaultValue: T

    init(bufferSize: Int, defaultValue: T, maxPoolSize: Int = 10) {
        self.bufferSize = bufferSize
        self.defaultValue = defaultValue
        self.maxPoolSize = maxPoolSize
    }

    func getBuffer() -> [T] {
        lock.lock()
        defer { lock.unlock() }
        
        if var buffer = availableBuffers.popLast() {
            // Reset buffer contents
            for i in 0..<buffer.count {
                buffer[i] = defaultValue
            }
            return buffer
        }
        
        // Create new buffer if pool is empty
        return Array(repeating: defaultValue, count: bufferSize)
    }

    func returnBuffer(_ buffer: [T]) {
        lock.lock()
        defer { lock.unlock() }
        
        // Only keep buffers if they're the right size and we haven't exceeded max pool size
        if buffer.count == bufferSize && availableBuffers.count < maxPoolSize {
            availableBuffers.append(buffer)
        }
        // Otherwise let it be deallocated naturally
    }
    
    func clear() {
        lock.lock()
        defer { lock.unlock() }
        availableBuffers.removeAll()
    }
}
