// PressureModel.swift
// Research-validated pressure model with confidence intervals and therapeutic status

import Foundation

struct PressureReading {
    let pressure: Float           // cmH2O
    let confidence: Float         // 0.0 to 1.0
    let therapeuticStatus: TherapeuticStatus
    let timestamp: TimeInterval
}

enum TherapeuticStatus {
    case tooLow      // < 10 cmH2O
    case therapeutic // 10-20 cmH2O
    case tooHigh     // > 20 cmH2O
    case unknown     // Invalid reading
}

class PressureModel {
    // Research-validated linear model coefficients from Figure 4
    // Pressure = slope * pitch + intercept
    private let slope: Float = 1.119        // From research paper
    private let intercept: Float = -4.659   // From research paper
    private let modelR2: Float = 0.886      // Model goodness of fit

    // Confidence calculation parameters
    private let validPitchRange: ClosedRange<Float> = 10.0...40.0
    private let therapeuticRange: ClosedRange<Float> = 10.0...20.0

    func estimatePressure(from pitchResult: PitchResult) -> PressureReading {
        let pitch = pitchResult.pitchHz

        // Apply validated linear model
        let rawPressure = slope * pitch + intercept
        let pressure = max(0, rawPressure) // Pressure cannot be negative

        // Calculate overall confidence
        let confidence = calculateOverallConfidence(
            pitchConfidence: pitchResult.confidence,
            pitch: pitch,
            pressure: pressure
        )

        // Determine therapeutic status
        let status = determineTherapeuticStatus(pressure: pressure, confidence: confidence)

        return PressureReading(
            pressure: pressure,
            confidence: confidence,
            therapeuticStatus: status,
            timestamp: pitchResult.timestamp
        )
    }

    private func calculateOverallConfidence(pitchConfidence: Float, pitch: Float, pressure: Float) -> Float {
        // Base confidence from pitch detection
        var confidence = pitchConfidence

        // Reduce confidence for out-of-range pitch values
        if !validPitchRange.contains(pitch) {
            let distanceFromRange = min(abs(pitch - validPitchRange.lowerBound),
                                      abs(pitch - validPitchRange.upperBound))
            let rangePenalty = min(0.5, distanceFromRange / 10.0) // Max 50% penalty
            confidence *= (1.0 - rangePenalty)
        }

        // Factor in model uncertainty (based on r²)
        let modelConfidence = sqrt(modelR2) // ~0.94 for r² = 0.886
        confidence *= modelConfidence

        return max(0, min(1, confidence))
    }

    private func determineTherapeuticStatus(pressure: Float, confidence: Float) -> TherapeuticStatus {
        // Require minimum confidence for valid status
        guard confidence >= 0.5 else { return .unknown }

        switch pressure {
        case ..<10.0:
            return .tooLow
        case therapeuticRange:
            return .therapeutic
        case 20.0...:
            return .tooHigh
        default:
            return .unknown
        }
    }

    // Additional utility methods
    func getPressureConfidenceInterval(for pressure: Float) -> (lower: Float, upper: Float) {
        // Calculate 95% confidence interval based on model r²
        let standardError = pressure * sqrt(1 - modelR2) * 1.96 // 95% CI
        return (lower: max(0, pressure - standardError),
                upper: pressure + standardError)
    }

    func getTherapeuticGuidance(for status: TherapeuticStatus) -> String {
        switch status {
        case .tooLow:
            return "Exhale more forcefully to increase pressure"
        case .therapeutic:
            return "Perfect! Maintain this breathing pattern"
        case .tooHigh:
            return "Reduce exhalation force slightly"
        case .unknown:
            return "Ensure proper device seal and breathing technique"
        }
    }
    
    // Get color for UI display based on therapeutic status
    func getStatusColor(for status: TherapeuticStatus) -> (red: Float, green: Float, blue: Float) {
        switch status {
        case .tooLow:
            return (1.0, 0.8, 0.0) // Yellow
        case .therapeutic:
            return (0.0, 0.8, 0.0) // Green
        case .tooHigh:
            return (1.0, 0.3, 0.0) // Red
        case .unknown:
            return (0.5, 0.5, 0.5) // Gray
        }
    }
    
    // Validate if pressure reading is clinically meaningful
    func isValidReading(_ reading: PressureReading) -> Bool {
        return reading.confidence >= 0.3 && 
               reading.pressure >= 0 && 
               reading.pressure <= 50 // Reasonable upper bound
    }
}
