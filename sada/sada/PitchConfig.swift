// PitchConfig.swift
// Core configuration for pitch detection algorithm per Acapella paper

import Foundation

struct PitchConfig {
    // Audio
    var sampleRate: Double = 44_100
    var chunkDuration: TimeInterval = 0.1 // seconds
    var downsampleFactor: Int = 45 // per paper

    // Detection window
    var minFreq: Float = 10
    var maxFreq: Float = 40

    // Baseline subtraction (moving average window ~ formant period 250 Hz @ 44.1kHz ≈ 176 samples)
    // Scale with sampleRate
    var baselineWindowSamples: Int {
        let period = sampleRate / 250.0
        return max(8, Int(period.rounded()))
    }

    // Autocorr acceptance threshold
    var autocorrThreshold: Float = 0.6

    // Temporal smoothing
    var decayRate: Float = 0.8
    var minAmp: Float = 2.0e-4

    // Derived
    var energySampleRate: Double { sampleRate / Double(downsampleFactor) }
    var chunkFrames: Int { Int((sampleRate * chunkDuration).rounded()) }
}

