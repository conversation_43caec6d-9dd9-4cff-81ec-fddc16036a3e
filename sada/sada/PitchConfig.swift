// PitchConfig.swift
// Enhanced configuration for pitch detection algorithm per Acapella research paper

import Foundation

struct PitchConfig {
    // Core algorithm parameters from research
    var sampleRate: Double = 44_100
    var minFreq: Float = 10.0
    var maxFreq: Float = 40.0
    var freqAccuracy: Float = 0.025        // Missing from current implementation
    var lowerFormantFreq: Int = 250        // Missing from current implementation
    var autocorrThreshold: Float = 0.6
    var decayRate: Float = 0.8
    var minAmp: Float = 2.0e-4

    // Processing parameters
    var chunkDuration: TimeInterval = 0.1
    var downsampleFactor: Int = 45

    // Energy detection parameters
    var energyThreshold: Float = 0.001
    var minExhalationDuration: TimeInterval = 0.5
    var maxSilenceDuration: TimeInterval = 0.3

    // Derived properties
    var energySampleRate: Double { sampleRate / Double(downsampleFactor) }
    var chunkFrames: Int { Int((sampleRate * chunkDuration).rounded()) }
    var baselineWindowSamples: Int { max(8, Int((sampleRate / Double(lowerFormantFreq)).rounded())) }

    // Run length parameters
    var maxRunLength: Int { Int(round(1.0 / (1.0 - decayRate))) } // = 5 when decayRate = 0.8

    // Optimal downsample factor based on frequency accuracy
    var optimalDownsampleFactor: Int {
        let nyquistTarget = maxFreq * 2.0 / (1.0 - freqAccuracy)
        return Int(sampleRate / Double(nyquistTarget))
    }
}

