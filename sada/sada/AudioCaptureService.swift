// AudioCaptureService.swift
// Enhanced audio capture with energy-based gating and session continuity

import Foundation
import AVFoundation

protocol AudioCaptureDelegate: AnyObject {
    func audioCapture(didDetect result: PitchResult)
    func audioCapture(didUpdateExhalation state: ExhalationState)
}

final class AudioCaptureService {
    private let engine = AVAudioEngine()
    private let session = AVAudioSession.sharedInstance()
    private var cfg: PitchConfig
    private var tracker: PitchTracker?

    // Session state management
    private var cumulativeSampleIndex: Int = 0
    private var sessionStartTime: TimeInterval = 0
    private var isSessionActive: Bool = false

    // Energy-based exhalation detection
    private var energyDetector: ExhalationDetector

    weak var delegate: AudioCaptureDelegate?

    init(config: PitchConfig) {
        self.cfg = config
        self.energyDetector = ExhalationDetector(config: config)
    }

    func start() throws {
        // Initialize session state
        cumulativeSampleIndex = 0
        sessionStartTime = Date().timeIntervalSince1970
        isSessionActive = true
        tracker = PitchTracker(config: cfg)
        energyDetector.reset()

        switch session.recordPermission {
        case .undetermined:
            session.requestRecordPermission { [weak self] granted in
                guard let self else { return }
                DispatchQueue.main.async {
                    if granted {
                        try? self.configureSession()
                        try? self.startEngine()
                    } else {
                        self.isSessionActive = false
                        // Notify UI via a dummy low-confidence result or ignore
                    }
                }
            }
        case .granted:
            try configureSession()
            try startEngine()
        case .denied:
            isSessionActive = false
            // Surface a readable error; keep throws signature
            throw NSError(domain: "Audio", code: 1, userInfo: [NSLocalizedDescriptionKey: "Microphone permission denied. Enable in Settings."])
        @unknown default:
            isSessionActive = false
            break
        }
    }

    func stop() {
        isSessionActive = false
        engine.inputNode.removeTap(onBus: 0)
        engine.stop()
        try? session.setActive(false)
        tracker = nil
        energyDetector.reset()
    }

    private func configureSession() throws {
        try session.setCategory(.record, mode: .measurement, options: [.allowBluetooth])
        try session.setPreferredSampleRate(cfg.sampleRate)
        try session.setPreferredIOBufferDuration(cfg.chunkDuration)
        try session.setActive(true)
    }

    private func startEngine() throws {
        let input = engine.inputNode

        // Use node's native format to avoid conversion issues; adapt tracker to actual sample rate
        input.installTap(onBus: 0, bufferSize: AVAudioFrameCount(cfg.chunkFrames), format: nil) { [weak self] buffer, when in
            guard let self = self, self.isSessionActive else { return }
            guard let chData = buffer.floatChannelData else { return }

            let ptr = chData[0]
            let frameCount = Int(buffer.frameLength)
            let bufferArray = Array(UnsafeBufferPointer(start: ptr, count: frameCount))
            let sr = buffer.format.sampleRate

            // Adapt tracker to actual sample rate if needed
            if self.tracker == nil || abs(self.cfg.sampleRate - sr) > 1 {
                self.cfg.sampleRate = sr
                self.tracker = PitchTracker(config: self.cfg)
            }

            // Energy-based gating
            let exhalationState = self.energyDetector.processBuffer(bufferArray)

            // Notify delegate of exhalation state
            DispatchQueue.main.async {
                self.delegate?.audioCapture(didUpdateExhalation: exhalationState)
            }

            // Only process pitch during exhalation
            if exhalationState.isExhaling {
                if let tracker = self.tracker,
                   let result = tracker.process(
                    buffer: ptr,
                    count: frameCount,
                    startIndex: self.cumulativeSampleIndex,
                    timestamp: when.sampleTime
                   ) {
                    DispatchQueue.main.async {
                        self.delegate?.audioCapture(didDetect: result)
                    }
                }
            }

            self.cumulativeSampleIndex += frameCount
        }

        engine.prepare()
        try engine.start()
    }
}

