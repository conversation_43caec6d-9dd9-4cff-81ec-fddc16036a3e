// AudioCaptureService.swift
// Captures microphone audio with AVAudioEngine and feeds PitchTracker

import Foundation
import AVFoundation

protocol AudioCaptureDelegate: AnyObject {
    func audioCapture(didDetect result: PitchResult)
}

final class AudioCaptureService {
    private let engine = AVAudioEngine()
    private let session = AVAudioSession.sharedInstance()
    private var cfg: PitchConfig
    private var tracker: PitchTracker?

    weak var delegate: AudioCaptureDelegate?

    init(config: PitchConfig) {
        self.cfg = config
    }

    func start() throws {
        switch session.recordPermission {
        case .undetermined:
            session.requestRecordPermission { [weak self] granted in
                guard let self else { return }
                DispatchQueue.main.async {
                    if granted {
                        try? self.configureSession()
                        try? self.startEngine()
                    } else {
                        // Notify UI via a dummy low-confidence result or ignore
                    }
                }
            }
        case .granted:
            try configureSession()
            try startEngine()
        case .denied:
            // Surface a readable error; keep throws signature
            throw NSError(domain: "Audio", code: 1, userInfo: [NSLocalizedDescriptionKey: "Microphone permission denied. Enable in Settings."])
        @unknown default:
            break
        }
    }

    func stop() {
        engine.inputNode.removeTap(onBus: 0)
        engine.stop()
        try? session.setActive(false)
        tracker = nil
    }

    private func configureSession() throws {
        try session.setCategory(.record, mode: .measurement, options: [.allowBluetooth])
        try session.setPreferredSampleRate(cfg.sampleRate)
        try session.setPreferredIOBufferDuration(cfg.chunkDuration)
        try session.setActive(true)
    }

    private func startEngine() throws {
        let input = engine.inputNode
        let nativeFormat = input.outputFormat(forBus: 0)

        // Use node's native format to avoid conversion issues; adapt tracker to actual sample rate
        input.installTap(onBus: 0, bufferSize: AVAudioFrameCount(cfg.chunkFrames), format: nil) { [weak self] buffer, when in
            guard let self else { return }
            guard let chData = buffer.floatChannelData else { return }
            let ptr = chData[0]
            let frameCount = Int(buffer.frameLength)
            let sr = buffer.format.sampleRate
            if self.tracker == nil || abs(self.cfg.sampleRate - sr) > 1 {
                // Reconfigure tracker to actual sample rate
                self.cfg.sampleRate = sr
                self.tracker = PitchTracker(config: self.cfg)
            }
            let timestamp = Date().timeIntervalSince1970
            if let tracker = self.tracker,
               let result = tracker.process(buffer: ptr, count: frameCount, startIndex: 0, timestamp: timestamp) {
                DispatchQueue.main.async {
                    self.delegate?.audioCapture(didDetect: result)
                }
            }
        }

        engine.prepare()
        try engine.start()
    }
}

