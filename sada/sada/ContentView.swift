//
//  ContentView.swift
//  sada
//
//  Created by <PERSON> on 12/08/2025.
//

import SwiftUI
import AVFoundation

final class PitchViewModel: ObservableObject, AudioCaptureDelegate {
    @Published var isRunning: Bool = false
    @Published var pitchText: String = "--"
    @Published var confidenceText: String = "--"
    @Published var pressureText: String = "--"
    @Published var statusText: String = "Idle"
    @Published var exhalationText: String = "--"
    @Published var energyText: String = "--"
    @Published var guidanceText: String = "Ready to start monitoring"
    @Published var therapeuticStatus: TherapeuticStatus = .unknown

    private let config = PitchConfig()
    private lazy var capture = AudioCaptureService(config: config)
    private let pressureModel = PressureModel()

    init() {
        capture.delegate = self
    }

    func start() {
        do {
            try capture.start()
            isRunning = true
            statusText = "Listening..."
        } catch {
            statusText = "Audio error: \(error.localizedDescription)"
        }
    }

    func stop() {
        capture.stop()
        isRunning = false
        statusText = "Stopped"
    }

    func audioCapture(didDetect result: PitchResult) {
        pitchText = String(format: "%.1f Hz", result.pitchHz)
        confidenceText = String(format: "%.2f", result.confidence)

        // Use research-validated pressure model
        let pressureReading = pressureModel.estimatePressure(from: result)
        pressureText = String(format: "%.1f cmH2O (%.0f%%)", pressureReading.pressure, pressureReading.confidence * 100)
        therapeuticStatus = pressureReading.therapeuticStatus
        guidanceText = pressureModel.getTherapeuticGuidance(for: pressureReading.therapeuticStatus)

        // Update status based on therapeutic range
        switch pressureReading.therapeuticStatus {
        case .tooLow:
            statusText = "Too Low"
        case .therapeutic:
            statusText = "Therapeutic Range"
        case .tooHigh:
            statusText = "Too High"
        case .unknown:
            statusText = "Uncertain"
        }
    }

    func audioCapture(didUpdateExhalation state: ExhalationState) {
        exhalationText = state.isExhaling ? "Exhaling" : "Not Exhaling"
        energyText = String(format: "%.4f", state.energy)
        if state.isExhaling {
            statusText = String(format: "Exhaling (%.1fs)", state.duration)
        } else if !isRunning {
            statusText = "Stopped"
        } else {
            statusText = "Waiting for exhalation..."
        }
    }
}

struct ContentView: View {
    @StateObject private var vm = PitchViewModel()

    var body: some View {
        VStack(spacing: 16) {
            Text("PEP Pitch Monitor").font(.title2).bold()
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Label { Text(vm.pitchText) } icon: { Image(systemName: "waveform") }
                    Label { Text(vm.confidenceText) } icon: { Image(systemName: "checkmark.seal") }
                    Label { Text(vm.pressureText) } icon: { Image(systemName: "gauge") }
                    Label { Text(vm.exhalationText) } icon: { Image(systemName: "lungs") }
                    Label { Text(vm.energyText) } icon: { Image(systemName: "bolt") }
                }
                Spacer()
            }
            .padding()
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(12)

            Text(vm.statusText)
                .font(.headline)
                .foregroundStyle(getStatusColor(for: vm.therapeuticStatus))

            Text(vm.guidanceText)
                .font(.subheadline)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
                .foregroundStyle(.secondary)

            HStack(spacing: 20) {
                Button(action: {
                    vm.isRunning ? vm.stop() : vm.start()
                }) {
                    Text(vm.isRunning ? "Stop" : "Start")
                        .font(.headline)
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .padding()
    }

    private func getStatusColor(for status: TherapeuticStatus) -> Color {
        switch status {
        case .tooLow:
            return .yellow
        case .therapeutic:
            return .green
        case .tooHigh:
            return .red
        case .unknown:
            return .gray
        }
    }
}

#Preview {
    ContentView()
}
