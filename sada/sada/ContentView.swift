//
//  ContentView.swift
//  sada
//
//  Created by <PERSON> on 12/08/2025.
//

import SwiftUI
import AVFoundation

final class PitchViewModel: ObservableObject, AudioCaptureDelegate {
    @Published var isRunning: Bool = false
    @Published var pitchText: String = "--"
    @Published var confidenceText: String = "--"
    @Published var pressureText: String = "--"
    @Published var statusText: String = "Idle"

    private let config = PitchConfig()
    private lazy var capture = AudioCaptureService(config: config)

    init() {
        capture.delegate = self
    }

    func start() {
        do {
            try capture.start()
            isRunning = true
            statusText = "Listening..."
        } catch {
            statusText = "Audio error: \(error.localizedDescription)"
        }
    }

    func stop() {
        capture.stop()
        isRunning = false
        statusText = "Stopped"
    }

    // Simple placeholder pressure mapping: P = a*Pitch + b (unknown coefficients). Use nominal mapping for demo.
    private func estimatePressure(from pitch: Float) -> Float {
        let a: Float = 0.8 // placeholder slope
        let b: Float = -0.0 // placeholder intercept
        return max(0, a * pitch + b)
    }

    func audioCapture(didDetect result: PitchResult) {
        pitchText = String(format: "%.1f Hz", result.pitchHz)
        confidenceText = String(format: "%.2f", result.confidence)
        let pressure = estimatePressure(from: result.pitchHz)
        pressureText = String(format: "%.1f cmH2O", pressure)
        if pressure < 10 { statusText = "Too Low" }
        else if pressure <= 20 { statusText = "Good" }
        else { statusText = "Too High" }
    }
}

struct ContentView: View {
    @StateObject private var vm = PitchViewModel()

    var body: some View {
        VStack(spacing: 16) {
            Text("PEP Pitch Monitor").font(.title2).bold()
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Label { Text(vm.pitchText) } icon: { Image(systemName: "waveform") }
                    Label { Text(vm.confidenceText) } icon: { Image(systemName: "checkmark.seal") }
                    Label { Text(vm.pressureText) } icon: { Image(systemName: "gauge") }
                }
                Spacer()
            }
            .padding()
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(12)

            Text(vm.statusText)
                .font(.headline)
                .foregroundStyle(vm.statusText == "Good" ? .green : (vm.statusText == "Too Low" ? .yellow : .red))

            HStack(spacing: 20) {
                Button(action: {
                    vm.isRunning ? vm.stop() : vm.start()
                }) {
                    Text(vm.isRunning ? "Stop" : "Start")
                        .font(.headline)
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .padding()
    }
}

#Preview {
    ContentView()
}
