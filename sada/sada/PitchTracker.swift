// PitchTracker.swift
// Implements core algorithm from AcapellaPitchDetectionV3 using Accelerate

import Foundation
import Accelerate

struct PitchResult {
    let pitchHz: Float
    let confidence: Float
    let amplitude: Float
    let timestamp: TimeInterval
}

final class PitchTracker {
    private let cfg: PitchConfig

    // Working buffers (reused to avoid allocations)
    private var baselineBuf: [Float] = []
    private var residualBuf: [Float] = []
    private var energyBuf: [Float] = []
    private var downsampledEnergy: [Float] = []
    private var smoothedEnergy: [Float] = []
    private var gaussianKernel: [Float] = []

    // Enhanced state for temporal smoothing
    private var runLength: Int = 0
    private var maPeriod: Float? = nil
    private var maAmplitude: Float? = nil
    private var maDerivative: Float? = nil
    private var lastPitchHz: Float? = nil
    private var lastValidLag: Int? = nil

    // Performance optimization
    private let bufferPool: BufferPool
    private let arrayPool: ArrayBufferPool<Float>

    init(config: PitchConfig) {
        self.cfg = config
        self.bufferPool = BufferPool(bufferSize: config.chunkFrames)
        self.arrayPool = ArrayBufferPool<Float>(bufferSize: config.chunkFrames, defaultValue: 0.0)
        prepareGaussianKernel()
    }

    // Prepare Gaussian kernel according to paper: sigma ≈ 0.2 * 45 * f_upper / Fs
    private func prepareGaussianKernel() {
        // Target FWHM ≈ 0.4 × period at f_upper (per paper)
        let FsDS = cfg.energySampleRate
        let periodUpperSamples = FsDS / Double(cfg.maxFreq) // in downsampled samples
        let targetFWHM = 0.4 * periodUpperSamples
        let sigmaSamplesDS = max(1e-3, targetFWHM / 2.355) // FWHM ≈ 2.355*sigma

        let half = max(2, Int(ceil(3.0 * sigmaSamplesDS)))
        let len = half * 2 + 1
        gaussianKernel = [Float](repeating: 0, count: len)
        let denom = 2.0 * pow(sigmaSamplesDS, 2)
        var sum: Double = 0
        for i in 0..<len {
            let x = Double(i - half)
            let v = exp(-(x*x)/denom)
            gaussianKernel[i] = Float(v)
            sum += v
        }
        if sum > 0 {
            vDSP_vsdiv(gaussianKernel, 1, [Float(sum)], &gaussianKernel, 1, vDSP_Length(len))
        }
    }

    // Process a PCM float buffer. startIndex can be buffer offset since session start for parity with paper.
    func process(buffer: UnsafePointer<Float>, count: Int, startIndex: Int, timestamp: TimeInterval) -> PitchResult? {
        guard count > 0 else { return nil }

        // Ensure buffers sized
        resizeBuffers(frameCount: count)

        // 1) Baseline smoothing (moving average) and residual
        let win = cfg.baselineWindowSamples
        if win > 1 && count > win {
            // Moving average via convolution with box kernel, then center-pad to original length
            var box = [Float](repeating: 1.0 / Float(win), count: win)
            let outLen = count - win + 1
            var core = [Float](repeating: 0, count: outLen)
            core.withUnsafeMutableBufferPointer { corePtr in
                vDSP_conv(buffer, 1, &box, 1, corePtr.baseAddress!, 1, vDSP_Length(count), vDSP_Length(win))
            }
            // Center align: place core into baselineBuf offset by win/2, pad edges with endpoints
            baselineBuf.withUnsafeMutableBufferPointer { dest in
                let pad = win / 2
                // Leading pad
                for i in 0..<pad { dest[i] = core.first ?? 0 }
                // Core
                for i in 0..<outLen { dest[i + pad] = core[i] }
                // Trailing pad
                let endStart = pad + outLen
                if endStart < dest.count {
                    for i in endStart..<dest.count { dest[i] = core.last ?? 0 }
                }
            }
        } else {
            // Not enough samples for smoothing; copy input
            baselineBuf = Array(UnsafeBufferPointer(start: buffer, count: count))
        }
        residualBuf.withUnsafeMutableBufferPointer { dest in
            vDSP_vsub(baselineBuf, 1, buffer, 1, dest.baseAddress!, 1, vDSP_Length(count))
        }

        // 2) Energy = residual^2
        energyBuf.withUnsafeMutableBufferPointer { dest in
            vDSP_vsq(residualBuf, 1, dest.baseAddress!, 1, vDSP_Length(count))
        }

        // 3) Downsample by averaging contiguous blocks (factor = downsampleFactor)
        let f = cfg.downsampleFactor
        let dsCount = count / f
        if dsCount <= 2 { return nil }
        downsampledEnergy.withUnsafeMutableBufferPointer { dest in
            dest.initialize(repeating: 0)
        }
        energyBuf.withUnsafeBufferPointer { src in
            var idx = 0
            var out = 0
            while idx + f <= count {
                var sum: Float = 0
                vDSP_sve(src.baseAddress! + idx, 1, &sum, vDSP_Length(f))
                downsampledEnergy[out] = sum / Float(f)
                out += 1
                idx += f
            }
        }
        let nDS = dsCount

        // 4) Gaussian smoothing via convolution; center-pad to original length
        let k = gaussianKernel
        let kCount = k.count
        if nDS > kCount {
            let outLen = nDS - kCount + 1
            var core = [Float](repeating: 0, count: outLen)
            core.withUnsafeMutableBufferPointer { corePtr in
                downsampledEnergy.withUnsafeBufferPointer { src in
                    var kernel = k
                    vDSP_conv(src.baseAddress!, 1, &kernel, 1, corePtr.baseAddress!, 1, vDSP_Length(nDS), vDSP_Length(kCount))
                }
            }
            smoothedEnergy = Array(repeating: 0, count: nDS)
            let pad = kCount / 2
            for i in 0..<pad { smoothedEnergy[i] = core.first ?? 0 }
            for i in 0..<outLen { smoothedEnergy[i + pad] = core[i] }
            let endStart = pad + outLen
            if endStart < nDS {
                for i in endStart..<nDS { smoothedEnergy[i] = core.last ?? 0 }
            }
        } else {
            smoothedEnergy = downsampledEnergy
        }

        // 5) Enhanced autocorrelation with stateful search
        guard let autocorrResult = performEconomicalAutocorrelation(smoothedEnergy: smoothedEnergy) else { return nil }

        // 6) Enhanced temporal smoothing with leap detection
        return performTemporalSmoothing(autocorrResult, timestamp: timestamp)

    }

    // MARK: - Enhanced Algorithm Components

    private func performEconomicalAutocorrelation(smoothedEnergy: [Float]) -> AutocorrelationResult? {
        let FsDS = cfg.energySampleRate
        let lagMin = Int((FsDS / Double(cfg.maxFreq)).rounded())
        let lagMax = Int((FsDS / Double(cfg.minFreq)).rounded())

        guard lagMax < smoothedEnergy.count && lagMin >= 1 else { return nil }

        // Stateful search: focus around last valid lag if available
        let searchLags = determineSearchLags(lagMin: lagMin, lagMax: lagMax)

        var bestLag = -1
        var bestVal: Float = -Float.infinity

        // Coarse search
        for lag in searchLags.coarse {
            let correlation = normalizedAutocorrelation(smoothedEnergy: smoothedEnergy, lag: lag)
            if correlation > bestVal {
                bestVal = correlation
                bestLag = lag
            }
        }

        guard bestLag > 0 else { return nil }

        // Fine search around best coarse result
        let fineStart = max(lagMin, bestLag - 2)
        let fineEnd = min(lagMax, bestLag + 2)

        for lag in fineStart...fineEnd {
            let correlation = normalizedAutocorrelation(smoothedEnergy: smoothedEnergy, lag: lag)
            if correlation > bestVal {
                bestVal = correlation
                bestLag = lag
            }
        }

        guard bestVal >= cfg.autocorrThreshold else { return nil }

        lastValidLag = bestLag

        let periodDS = Float(bestLag)
        let periodSec = periodDS / Float(FsDS)
        let pitch = 1.0 / periodSec

        return AutocorrelationResult(pitch: pitch, confidence: bestVal, lag: bestLag)
    }

    private func determineSearchLags(lagMin: Int, lagMax: Int) -> (coarse: [Int], fine: Range<Int>) {
        if let lastLag = lastValidLag {
            // Stateful search: focus around last valid lag
            let searchRadius = max(2, Int(Float(lastLag) * 0.1)) // 10% of last lag
            let centerStart = max(lagMin, lastLag - searchRadius)
            let centerEnd = min(lagMax, lastLag + searchRadius)

            let coarseStep = max(1, searchRadius / 5)
            let coarseLags = stride(from: centerStart, through: centerEnd, by: coarseStep).map { $0 }

            return (coarse: coarseLags, fine: centerStart..<centerEnd)
        } else {
            // Full search for first detection
            let coarseStep = max(1, (lagMax - lagMin) / 20)
            let coarseLags = stride(from: lagMin, through: lagMax, by: coarseStep).map { $0 }

            return (coarse: coarseLags, fine: lagMin..<lagMax)
        }
    }

    private func normalizedAutocorrelation(smoothedEnergy: [Float], lag: Int) -> Float {
        let n = smoothedEnergy.count - lag
        if n <= 2 { return 0 }

        var dot: Float = 0
        var e0: Float = 0
        var e1: Float = 0

        smoothedEnergy.withUnsafeBufferPointer { s in
            let base = s.baseAddress!
            vDSP_dotpr(base, 1, base + lag, 1, &dot, vDSP_Length(n))
            vDSP_dotpr(base, 1, base, 1, &e0, vDSP_Length(n))
            vDSP_dotpr(base + lag, 1, base + lag, 1, &e1, vDSP_Length(n))
        }

        let eps: Float = 1e-12
        let denom = sqrtf(max(e0, eps) * max(e1, eps))
        return denom > 0 ? (dot / denom) : 0
    }

    private func performTemporalSmoothing(_ result: AutocorrelationResult, timestamp: TimeInterval) -> PitchResult? {
        // Enhanced run length logic with leap detection
        let derivative: Float
        if let last = lastPitchHz {
            derivative = result.pitch - last

            // Leap detection
            let expectedPitch = last
            let leap = abs(result.pitch - expectedPitch)
            let maxExpectedLeap = expectedPitch * 0.1 // 10% tolerance

            if leap > maxExpectedLeap {
                // Reduce run length based on leap magnitude
                let leapPenalty = Int(leap / maxExpectedLeap)
                runLength = max(0, runLength - leapPenalty)
            } else {
                runLength = min(cfg.maxRunLength, runLength + 1)
            }
        } else {
            derivative = 0
            runLength = 1
        }

        lastPitchHz = result.pitch

        // Calculate adaptive mixing factor
        let mix = min(cfg.decayRate, 1.0 - 1.0 / Float(max(1, runLength)))

        // Update moving averages
        let newPeriod = 1.0 / result.pitch
        if let prev = maPeriod {
            maPeriod = prev * mix + newPeriod * (1 - mix)
        } else {
            maPeriod = newPeriod
        }

        if let prevA = maAmplitude {
            maAmplitude = prevA * mix + result.confidence * (1 - mix)
        } else {
            maAmplitude = result.confidence
        }

        if let prevD = maDerivative {
            maDerivative = prevD * mix + derivative * (1 - mix)
        } else {
            maDerivative = derivative
        }

        guard let maP = maPeriod, let maA = maAmplitude else { return nil }

        let finalPitch = max(0.0, 1.0 / maP)

        // Amplitude threshold check
        guard maA >= cfg.minAmp else { return nil }

        return PitchResult(
            pitchHz: finalPitch,
            confidence: result.confidence,
            amplitude: maA,
            timestamp: timestamp
        )
    }

    private func resizeBuffers(frameCount: Int) {
        if baselineBuf.count != frameCount { baselineBuf = Array(repeating: 0, count: frameCount) }
        if residualBuf.count != frameCount { residualBuf = Array(repeating: 0, count: frameCount) }
        if energyBuf.count != frameCount { energyBuf = Array(repeating: 0, count: frameCount) }
        let dsCount = max(0, frameCount / cfg.downsampleFactor)
        if downsampledEnergy.count != dsCount { downsampledEnergy = Array(repeating: 0, count: dsCount) }
        if smoothedEnergy.count != dsCount { smoothedEnergy = Array(repeating: 0, count: dsCount) }
    }
}

// MARK: - Supporting Structures

struct AutocorrelationResult {
    let pitch: Float
    let confidence: Float
    let lag: Int
}

