// ExhalationDetector.swift
// Sophisticated energy-based exhalation detection with adaptive thresholding

import Foundation

struct ExhalationState {
    let isExhaling: Bool
    let energy: Float
    let confidence: Float
    let duration: TimeInterval
}

class ExhalationDetector {
    private let config: PitchConfig
    private var energyHistory: [Float] = []
    private var isCurrentlyExhaling: Bool = false
    private var exhalationStartTime: TimeInterval = 0
    private var lastUpdateTime: TimeInterval = 0
    private var adaptiveThreshold: Float

    init(config: PitchConfig) {
        self.config = config
        self.adaptiveThreshold = config.energyThreshold
    }

    func processBuffer(_ buffer: [Float]) -> ExhalationState {
        let currentTime = Date().timeIntervalSince1970
        let rms = calculateRMS(buffer)

        // Update energy history for adaptive threshold
        energyHistory.append(rms)
        if energyHistory.count > 100 { // Keep last 10 seconds at 10Hz
            energyHistory.removeFirst()
        }

        // Calculate adaptive threshold based on recent history
        updateAdaptiveThreshold()

        // State machine for exhalation detection
        let wasExhaling = isCurrentlyExhaling
        let shouldBeExhaling = rms > adaptiveThreshold

        if shouldBeExhaling && !wasExhaling {
            // Start of exhalation
            isCurrentlyExhaling = true
            exhalationStartTime = currentTime
        } else if !shouldBeExhaling && wasExhaling {
            // Potential end of exhalation - add hysteresis
            let exhalationDuration = currentTime - exhalationStartTime
            if exhalationDuration >= config.minExhalationDuration {
                isCurrentlyExhaling = false
            }
        }

        let duration = isCurrentlyExhaling ? currentTime - exhalationStartTime : 0
        let confidence = calculateConfidence(rms: rms, threshold: adaptiveThreshold)

        lastUpdateTime = currentTime

        return ExhalationState(
            isExhaling: isCurrentlyExhaling,
            energy: rms,
            confidence: confidence,
            duration: duration
        )
    }

    private func calculateRMS(_ buffer: [Float]) -> Float {
        let sumSquares = buffer.reduce(0) { $0 + $1 * $1 }
        return sqrt(sumSquares / Float(buffer.count))
    }

    private func updateAdaptiveThreshold() {
        guard energyHistory.count > 10 else { return }

        // Use median of recent quiet periods as noise floor
        let sortedHistory = energyHistory.sorted()
        let noiseFloor = sortedHistory[sortedHistory.count / 4] // 25th percentile

        // Set threshold as multiple of noise floor
        adaptiveThreshold = max(config.energyThreshold, noiseFloor * 3.0)
    }

    private func calculateConfidence(rms: Float, threshold: Float) -> Float {
        if rms <= threshold { return 0.0 }

        // Confidence increases with signal strength above threshold
        let ratio = rms / threshold
        return min(1.0, (ratio - 1.0) / 2.0) // Saturates at 3x threshold
    }
    
    // Reset detector state for new session
    func reset() {
        energyHistory.removeAll()
        isCurrentlyExhaling = false
        exhalationStartTime = 0
        lastUpdateTime = 0
        adaptiveThreshold = config.energyThreshold
    }
    
    // Get current adaptive threshold for debugging
    var currentThreshold: Float {
        return adaptiveThreshold
    }
}
