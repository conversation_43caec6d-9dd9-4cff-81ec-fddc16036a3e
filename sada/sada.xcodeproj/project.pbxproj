// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		CDDF250B2E4B638A0006C14F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = CDDF24F22E4B63860006C14F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CDDF24F92E4B63860006C14F;
			remoteInfo = sada;
		};
		CDDF25152E4B638A0006C14F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = CDDF24F22E4B63860006C14F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CDDF24F92E4B63860006C14F;
			remoteInfo = sada;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		CDDF24FA2E4B63860006C14F /* sada.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = sada.app; sourceTree = BUILT_PRODUCTS_DIR; };
		CDDF250A2E4B638A0006C14F /* sadaTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = sadaTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		CDDF25142E4B638A0006C14F /* sadaUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = sadaUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		CDDF24FC2E4B63860006C14F /* sada */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = sada;
			sourceTree = "<group>";
		};
		CDDF250D2E4B638A0006C14F /* sadaTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = sadaTests;
			sourceTree = "<group>";
		};
		CDDF25172E4B638A0006C14F /* sadaUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = sadaUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		CDDF24F72E4B63860006C14F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CDDF25072E4B638A0006C14F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CDDF25112E4B638A0006C14F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		CDDF24F12E4B63860006C14F = {
			isa = PBXGroup;
			children = (
				CDDF24FC2E4B63860006C14F /* sada */,
				CDDF250D2E4B638A0006C14F /* sadaTests */,
				CDDF25172E4B638A0006C14F /* sadaUITests */,
				CDDF24FB2E4B63860006C14F /* Products */,
			);
			sourceTree = "<group>";
		};
		CDDF24FB2E4B63860006C14F /* Products */ = {
			isa = PBXGroup;
			children = (
				CDDF24FA2E4B63860006C14F /* sada.app */,
				CDDF250A2E4B638A0006C14F /* sadaTests.xctest */,
				CDDF25142E4B638A0006C14F /* sadaUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		CDDF24F92E4B63860006C14F /* sada */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CDDF251E2E4B638A0006C14F /* Build configuration list for PBXNativeTarget "sada" */;
			buildPhases = (
				CDDF24F62E4B63860006C14F /* Sources */,
				CDDF24F72E4B63860006C14F /* Frameworks */,
				CDDF24F82E4B63860006C14F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				CDDF24FC2E4B63860006C14F /* sada */,
			);
			name = sada;
			packageProductDependencies = (
			);
			productName = sada;
			productReference = CDDF24FA2E4B63860006C14F /* sada.app */;
			productType = "com.apple.product-type.application";
		};
		CDDF25092E4B638A0006C14F /* sadaTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CDDF25212E4B638A0006C14F /* Build configuration list for PBXNativeTarget "sadaTests" */;
			buildPhases = (
				CDDF25062E4B638A0006C14F /* Sources */,
				CDDF25072E4B638A0006C14F /* Frameworks */,
				CDDF25082E4B638A0006C14F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				CDDF250C2E4B638A0006C14F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				CDDF250D2E4B638A0006C14F /* sadaTests */,
			);
			name = sadaTests;
			packageProductDependencies = (
			);
			productName = sadaTests;
			productReference = CDDF250A2E4B638A0006C14F /* sadaTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		CDDF25132E4B638A0006C14F /* sadaUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CDDF25242E4B638A0006C14F /* Build configuration list for PBXNativeTarget "sadaUITests" */;
			buildPhases = (
				CDDF25102E4B638A0006C14F /* Sources */,
				CDDF25112E4B638A0006C14F /* Frameworks */,
				CDDF25122E4B638A0006C14F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				CDDF25162E4B638A0006C14F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				CDDF25172E4B638A0006C14F /* sadaUITests */,
			);
			name = sadaUITests;
			packageProductDependencies = (
			);
			productName = sadaUITests;
			productReference = CDDF25142E4B638A0006C14F /* sadaUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		CDDF24F22E4B63860006C14F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					CDDF24F92E4B63860006C14F = {
						CreatedOnToolsVersion = 16.2;
					};
					CDDF25092E4B638A0006C14F = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = CDDF24F92E4B63860006C14F;
					};
					CDDF25132E4B638A0006C14F = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = CDDF24F92E4B63860006C14F;
					};
				};
			};
			buildConfigurationList = CDDF24F52E4B63860006C14F /* Build configuration list for PBXProject "sada" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = CDDF24F12E4B63860006C14F;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = CDDF24FB2E4B63860006C14F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				CDDF24F92E4B63860006C14F /* sada */,
				CDDF25092E4B638A0006C14F /* sadaTests */,
				CDDF25132E4B638A0006C14F /* sadaUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		CDDF24F82E4B63860006C14F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CDDF25082E4B638A0006C14F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CDDF25122E4B638A0006C14F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		CDDF24F62E4B63860006C14F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CDDF25062E4B638A0006C14F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CDDF25102E4B638A0006C14F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		CDDF250C2E4B638A0006C14F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = CDDF24F92E4B63860006C14F /* sada */;
			targetProxy = CDDF250B2E4B638A0006C14F /* PBXContainerItemProxy */;
		};
		CDDF25162E4B638A0006C14F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = CDDF24F92E4B63860006C14F /* sada */;
			targetProxy = CDDF25152E4B638A0006C14F /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		CDDF251C2E4B638A0006C14F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		CDDF251D2E4B638A0006C14F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		CDDF251F2E4B638A0006C14F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"sada/Preview Content\"";
				DEVELOPMENT_TEAM = 6R8999W8FA;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Microphone access is needed to detect the PEP device pitch for therapy feedback.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.sada.sada-fresh";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		CDDF25202E4B638A0006C14F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"sada/Preview Content\"";
				DEVELOPMENT_TEAM = 6R8999W8FA;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Microphone access is needed to detect the PEP device pitch for therapy feedback.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.sada.sada-fresh";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		CDDF25222E4B638A0006C14F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6R8999W8FA;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sada.sadaTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/sada.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/sada";
			};
			name = Debug;
		};
		CDDF25232E4B638A0006C14F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6R8999W8FA;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sada.sadaTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/sada.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/sada";
			};
			name = Release;
		};
		CDDF25252E4B638A0006C14F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6R8999W8FA;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sada.sadaUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = sada;
			};
			name = Debug;
		};
		CDDF25262E4B638A0006C14F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6R8999W8FA;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sada.sadaUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = sada;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		CDDF24F52E4B63860006C14F /* Build configuration list for PBXProject "sada" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CDDF251C2E4B638A0006C14F /* Debug */,
				CDDF251D2E4B638A0006C14F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CDDF251E2E4B638A0006C14F /* Build configuration list for PBXNativeTarget "sada" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CDDF251F2E4B638A0006C14F /* Debug */,
				CDDF25202E4B638A0006C14F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CDDF25212E4B638A0006C14F /* Build configuration list for PBXNativeTarget "sadaTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CDDF25222E4B638A0006C14F /* Debug */,
				CDDF25232E4B638A0006C14F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CDDF25242E4B638A0006C14F /* Build configuration list for PBXNativeTarget "sadaUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CDDF25252E4B638A0006C14F /* Debug */,
				CDDF25262E4B638A0006C14F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = CDDF24F22E4B63860006C14F /* Project object */;
}
